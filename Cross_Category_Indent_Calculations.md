# Cross-Category Indent Calculations Documentation

## Overview

Cross-category indents are a critical component of the inventory reconciliation system in Digitory. They implement a **bidirectional zero-sum system** that tracks inventory transfers between different categories through shared workareas, ensuring accurate inventory reconciliation across the entire system.

## Core Concept

### What are Cross-Category Indents?

Cross-category indents occur when inventory items from one category are transferred (indented) to a workarea that belongs to a different category. This creates a cross-category flow that needs special handling in reconciliation calculations.

### Key Principles

1. **Bidirectional Zero-Sum System**: Every cross-category indent creates both a negative entry (for the giving category) and a positive entry (for the receiving category), ensuring the total system balance remains zero.

2. **Workarea-Based Detection**: Cross-category flows are detected by analyzing which categories have mapping rights to specific workareas.

3. **Equal Distribution**: When multiple categories share ownership of a workarea, the indent value is distributed equally among all receiving categories.

## Calculation Process

### Step 1: Data Collection

The system collects indent data from the consumption dataframe:

```python
# Extract indent information
unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
indent_qty = float(row.get('WorkArea Indent', 0) or 0)
indent_value = indent_qty * unit_price
```

**Formula**: `Indent Value = Indent Quantity × Unit Price (WAC)`

### Step 2: Workarea Ownership Mapping

The system builds a mapping of which categories have rights to each workarea:

```python
# Build category to workareas mapping
category_to_workareas = {}
for mapping in category_workarea_mappings:
    category_name = mapping['categoryName']
    work_areas = mapping['workAreas']
    category_to_workareas[category_name] = work_areas
```

### Step 3: Cross-Category Detection

For each indent transaction, the system determines if it's cross-category:

```python
# Find workarea owner categories
workarea_owner_categories = []
for category_name, work_areas in category_to_workareas.items():
    if workarea in work_areas:
        workarea_owner_categories.append(category_name)

# Check if cross-category
cross_category_owners = [owner for owner in workarea_owner_categories 
                        if owner != indent_category]
```

**Logic**: An indent is cross-category if the source category is different from any of the workarea owner categories.

### Step 4: Bidirectional Entries

#### For the Giving Category (Source):
```python
# Negative entry for giving category
reconciliation_table[indent_category]['subcategories'][subcategory]['transfer_details']['cross_category_indents'] -= indent_value
```

#### For the Receiving Categories (Destinations):
```python
# Positive entry for each receiving category
distributed_value = indent_value / len(cross_category_owners)
reconciliation_table[workarea_owner_category]['subcategories'][special_subcat_name]['transfer_details']['cross_category_indents'] += distributed_value
```

**Special Subcategory**: Receiving categories get a special subcategory called `"Goods from Other Categories' Indents"` to track these inbound transfers.

## Mathematical Formula

### Basic Cross-Category Indent Calculation:

```
For Source Category:
Cross-Category Indent = -Indent_Value

For Each Receiving Category:
Cross-Category Indent = +Indent_Value / Number_of_Receiving_Categories
```

### Integration with Reconciliation Formula:

The cross-category indents are integrated into the main reconciliation formula:

```
Kitchen Net Transfer = Transfer_In - Transfer_Out - Return_To_Store + Spoilage_Adjustments + Cross_Category_Indents

Final Consumption = Opening_Stock + Purchases + Kitchen_Net_Transfer - Closing_Stock
```

## Example Scenario

### Setup:
- **Category A** maps to: Kitchen1, Kitchen2
- **Category B** maps to: Kitchen2, Kitchen3
- **Category C** maps to: Kitchen3

### Transaction:
- Category A indents ₹1,000 worth of items to Kitchen2

### Calculation:
1. **Workarea Kitchen2 owners**: Category A, Category B
2. **Cross-category owners for Category A**: Category B
3. **Entries created**:
   - Category A: -₹1,000 (giving)
   - Category B: +₹1,000 (receiving, since only 1 receiving category)

### Result:
- Category A shows ₹1,000 reduction in cross-category indents
- Category B shows ₹1,000 increase under "Goods from Other Categories' Indents"
- System total: -₹1,000 + ₹1,000 = ₹0 (zero-sum maintained)

## Same-Category Indents

When an indent occurs within the same category (source category = workarea owner category):

```python
if not cross_category_owners:
    # Same category indent - populate indent field for UI display 
    # but don't affect reconciliation (already accounted for in purchases)
    reconciliation_table[indent_category]['subcategories'][subcategory]['transfer_details']['indent'] += indent_value
```

**Important**: Same-category indents are tracked for display purposes but don't affect reconciliation calculations as they're already accounted for in the purchase data.

## Transfer Details Logging

The system maintains detailed logs of all cross-category transfers:

```python
transfer_details_log.append({
    'source_category': indent_category,
    'source_subcategory': subcategory,
    'destination_category': workarea_owner_category,
    'destination_subcategory': special_subcat_name,
    'workarea': workarea,
    'amount': distributed_value,
    'transfer_type': 'Cross-Category Indent',
    'direction': 'outbound' if workarea_owner_category == indent_category else 'inbound'
})
```

## Integration with Dashboard Tables

Cross-category indents appear in several dashboard components:

### 1. Reconciliation Tables
- Source categories show negative values in their original subcategories
- Receiving categories show positive values under "Goods from Other Categories' Indents"

### 2. Transfer Tables
The workarea transfer table includes cross-category indents in the formula:
```
Net Transfer = Transfer In - Transfer Out - Return To Store + Spoilage + Cross-Category Indents
```

### 3. Group-Level Aggregation
Cross-category indents are aggregated at the department group level:
```python
group_data[group_name]['transfer_details']['cross_category_indents'] += details.get('cross_category_indents', 0)
```

## Key Benefits

1. **Accurate Reconciliation**: Ensures inventory movements between categories are properly tracked
2. **Zero-Sum Integrity**: Maintains system-wide inventory balance
3. **Transparency**: Provides clear visibility into cross-category flows
4. **Audit Trail**: Detailed logging enables tracking of all transfers

## Technical Implementation Notes

- **Threshold**: Only indents with absolute value > ₹0.01 are processed
- **Error Handling**: Invalid data entries are skipped with proper exception handling
- **Performance**: Efficient data structures minimize processing overhead
- **Scalability**: Handles multiple categories and workareas without performance degradation

## Conclusion

The cross-category indent system ensures accurate inventory reconciliation by implementing a sophisticated bidirectional tracking mechanism. This system maintains inventory integrity across category boundaries while providing complete transparency and auditability of all cross-category inventory movements.
