# Cross-Category Indent Calculations Documentation

## Overview

Cross-category indents are a critical component of the inventory reconciliation system in Digitory. They implement a **bidirectional zero-sum system** that tracks inventory transfers between different categories through shared workareas, ensuring accurate inventory reconciliation across the entire system.

## Core Concept

### What are Cross-Category Indents?

Cross-category indents occur when inventory items from one category are transferred (indented) to a workarea that belongs to a different category. This creates a cross-category flow that needs special handling in reconciliation calculations.

### Key Principles

1. **Bidirectional Zero-Sum System**: Every cross-category indent creates both a negative entry (for the giving category) and a positive entry (for the receiving category), ensuring the total system balance remains zero.

2. **Workarea-Based Detection**: Cross-category flows are detected by analyzing which categories have mapping rights to specific workareas.

3. **Equal Distribution**: When multiple categories share ownership of a workarea, the indent value is distributed equally among all receiving categories.

## Calculation Process

### Step 1: Data Collection

The system collects indent data from the consumption dataframe:

```python
# Extract indent information
unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
indent_qty = float(row.get('WorkArea Indent', 0) or 0)
indent_value = indent_qty * unit_price
```

**Formula**: `Indent Value = Indent Quantity × Unit Price (WAC)`

### Step 2: Workarea Ownership Mapping

The system builds a mapping of which categories have rights to each workarea:

```python
# Build category to workareas mapping
category_to_workareas = {}
for mapping in category_workarea_mappings:
    category_name = mapping['categoryName']
    work_areas = mapping['workAreas']
    category_to_workareas[category_name] = work_areas
```

### Step 3: Cross-Category Detection

For each indent transaction, the system determines if it's cross-category:

```python
# Find workarea owner categories
workarea_owner_categories = []
for category_name, work_areas in category_to_workareas.items():
    if workarea in work_areas:
        workarea_owner_categories.append(category_name)

# Check if cross-category
cross_category_owners = [owner for owner in workarea_owner_categories 
                        if owner != indent_category]
```

**Logic**: An indent is cross-category if the source category is different from any of the workarea owner categories.

### Step 4: Bidirectional Entries

#### For the Giving Category (Source):
```python
# Negative entry for giving category
reconciliation_table[indent_category]['subcategories'][subcategory]['transfer_details']['cross_category_indents'] -= indent_value
```

#### For the Receiving Categories (Destinations):
```python
# Positive entry for each receiving category
distributed_value = indent_value / len(cross_category_owners)
reconciliation_table[workarea_owner_category]['subcategories'][special_subcat_name]['transfer_details']['cross_category_indents'] += distributed_value
```

**Special Subcategory**: Receiving categories get a special subcategory called `"Goods from Other Categories' Indents"` to track these inbound transfers.

## Mathematical Formula

### Basic Cross-Category Indent Calculation:

```
For Source Category:
Cross-Category Indent = -Indent_Value

For Each Receiving Category:
Cross-Category Indent = +Indent_Value / Number_of_Receiving_Categories
```

### Integration with Reconciliation Formula:

The cross-category indents are integrated into the main reconciliation formula:

```
Kitchen Net Transfer = Transfer_In - Transfer_Out - Return_To_Store + Spoilage_Adjustments + Cross_Category_Indents

Final Consumption = Opening_Stock + Purchases + Kitchen_Net_Transfer - Closing_Stock
```

## Detailed Examples

### Example 1: Simple Cross-Category Transfer

#### Setup:
- **FOOD Category** maps to: Main Kitchen, Prep Kitchen
- **BEVERAGE Category** maps to: Prep Kitchen, Bar Kitchen
- **BAKERY Category** maps to: Bakery Kitchen

#### Transaction:
- FOOD Category indents ₹2,000 worth of flour to Prep Kitchen
- Item: Flour (ItemCode: FL001)
- Quantity: 50 kg
- Unit Price (WAC): ₹40/kg
- Total Value: 50 × ₹40 = ₹2,000

#### Step-by-Step Calculation:

1. **Identify Workarea Owners for "Prep Kitchen"**:
   - FOOD Category ✓ (owns Prep Kitchen)
   - BEVERAGE Category ✓ (owns Prep Kitchen)
   - BAKERY Category ✗ (doesn't own Prep Kitchen)

2. **Determine Cross-Category Status**:
   - Source Category: FOOD
   - Workarea Owners: [FOOD, BEVERAGE]
   - Cross-Category Owners: [BEVERAGE] (excluding source FOOD)
   - **Result**: This IS a cross-category indent

3. **Create Bidirectional Entries**:
   - **FOOD Category (Source)**: -₹2,000
     - Subcategory: "Flour"
     - Field: `cross_category_indents = -2000`

   - **BEVERAGE Category (Receiver)**: +₹2,000
     - Subcategory: "Goods from Other Categories' Indents"
     - Field: `cross_category_indents = +2000`

4. **Final Impact on Reconciliation**:
   ```
   FOOD Category:
   Kitchen Net Transfer = Transfer_In - Transfer_Out - Return_To_Store + Spoilage + (-2000)

   BEVERAGE Category:
   Kitchen Net Transfer = Transfer_In - Transfer_Out - Return_To_Store + Spoilage + (+2000)
   ```

#### Result Summary:
- **FOOD Category**: Shows ₹2,000 reduction (gave away flour)
- **BEVERAGE Category**: Shows ₹2,000 increase (received flour from FOOD)
- **System Balance**: -₹2,000 + ₹2,000 = ₹0 ✓

---

### Example 2: Multiple Receiving Categories

#### Setup:
- **FOOD Category** maps to: Main Kitchen
- **BEVERAGE Category** maps to: Shared Kitchen, Bar
- **BAKERY Category** maps to: Shared Kitchen, Bakery Area
- **DESSERT Category** maps to: Shared Kitchen, Dessert Station

#### Transaction:
- FOOD Category indents ₹3,000 worth of sugar to Shared Kitchen

#### Calculation:

1. **Identify Workarea Owners for "Shared Kitchen"**:
   - FOOD Category ✗ (doesn't own Shared Kitchen)
   - BEVERAGE Category ✓ (owns Shared Kitchen)
   - BAKERY Category ✓ (owns Shared Kitchen)
   - DESSERT Category ✓ (owns Shared Kitchen)

2. **Cross-Category Analysis**:
   - Source Category: FOOD
   - Workarea Owners: [BEVERAGE, BAKERY, DESSERT]
   - Cross-Category Owners: [BEVERAGE, BAKERY, DESSERT] (all are different from FOOD)
   - **Result**: This IS a cross-category indent with 3 receivers

3. **Distribution Calculation**:
   - Total Indent Value: ₹3,000
   - Number of Receiving Categories: 3
   - **Distributed Value per Category**: ₹3,000 ÷ 3 = ₹1,000

4. **Bidirectional Entries**:
   - **FOOD Category (Source)**: -₹3,000
   - **BEVERAGE Category (Receiver)**: +₹1,000
   - **BAKERY Category (Receiver)**: +₹1,000
   - **DESSERT Category (Receiver)**: +₹1,000

#### Result Summary:
- **FOOD Category**: -₹3,000 (gave away sugar)
- **BEVERAGE Category**: +₹1,000 (received 1/3 share)
- **BAKERY Category**: +₹1,000 (received 1/3 share)
- **DESSERT Category**: +₹1,000 (received 1/3 share)
- **System Balance**: -₹3,000 + ₹1,000 + ₹1,000 + ₹1,000 = ₹0 ✓

---

### Example 3: Same-Category Indent (No Cross-Category Effect)

#### Setup:
- **FOOD Category** maps to: Main Kitchen, Prep Kitchen

#### Transaction:
- FOOD Category indents ₹1,500 worth of vegetables to Main Kitchen

#### Calculation:

1. **Identify Workarea Owners for "Main Kitchen"**:
   - FOOD Category ✓ (owns Main Kitchen)

2. **Cross-Category Analysis**:
   - Source Category: FOOD
   - Workarea Owners: [FOOD]
   - Cross-Category Owners: [] (empty - source same as owner)
   - **Result**: This is NOT a cross-category indent

3. **Handling**:
   - **No reconciliation impact** (already accounted for in purchases)
   - **UI Display Only**:
     ```
     FOOD Category -> Vegetables subcategory:
     transfer_details.indent = +1500 (for display purposes)
     ```

#### Result Summary:
- **FOOD Category**: No change in `cross_category_indents`
- **System Balance**: No impact on reconciliation calculations
- **Display**: Shows ₹1,500 in indent field for transparency

## Same-Category Indents

When an indent occurs within the same category (source category = workarea owner category):

```python
if not cross_category_owners:
    # Same category indent - populate indent field for UI display 
    # but don't affect reconciliation (already accounted for in purchases)
    reconciliation_table[indent_category]['subcategories'][subcategory]['transfer_details']['indent'] += indent_value
```

**Important**: Same-category indents are tracked for display purposes but don't affect reconciliation calculations as they're already accounted for in the purchase data.

## Transfer Details Logging

The system maintains detailed logs of all cross-category transfers:

```python
transfer_details_log.append({
    'source_category': indent_category,
    'source_subcategory': subcategory,
    'destination_category': workarea_owner_category,
    'destination_subcategory': special_subcat_name,
    'workarea': workarea,
    'amount': distributed_value,
    'transfer_type': 'Cross-Category Indent',
    'direction': 'outbound' if workarea_owner_category == indent_category else 'inbound'
})
```

## Integration with Dashboard Tables

Cross-category indents appear in several dashboard components:

### 1. Reconciliation Tables
- Source categories show negative values in their original subcategories
- Receiving categories show positive values under "Goods from Other Categories' Indents"

### 2. Transfer Tables
The workarea transfer table includes cross-category indents in the formula:
```
Net Transfer = Transfer In - Transfer Out - Return To Store + Spoilage + Cross-Category Indents
```

### 3. Group-Level Aggregation
Cross-category indents are aggregated at the department group level:
```python
group_data[group_name]['transfer_details']['cross_category_indents'] += details.get('cross_category_indents', 0)
```

## Key Benefits

1. **Accurate Reconciliation**: Ensures inventory movements between categories are properly tracked
2. **Zero-Sum Integrity**: Maintains system-wide inventory balance
3. **Transparency**: Provides clear visibility into cross-category flows
4. **Audit Trail**: Detailed logging enables tracking of all transfers

## Technical Implementation Notes

- **Threshold**: Only indents with absolute value > ₹0.01 are processed
- **Error Handling**: Invalid data entries are skipped with proper exception handling
- **Performance**: Efficient data structures minimize processing overhead
- **Scalability**: Handles multiple categories and workareas without performance degradation

---

### Example 4: Complex Multi-Transaction Scenario

#### Setup:
- **FOOD Category** maps to: Main Kitchen, Cold Kitchen
- **BEVERAGE Category** maps to: Cold Kitchen, Bar
- **BAKERY Category** maps to: Main Kitchen, Bakery Area

#### Multiple Transactions in Same Period:

**Transaction 1**: FOOD → Cold Kitchen (₹2,000)
- Receivers: BEVERAGE (₹2,000)

**Transaction 2**: BEVERAGE → Main Kitchen (₹1,500)
- Receivers: FOOD, BAKERY (₹750 each)

**Transaction 3**: BAKERY → Cold Kitchen (₹3,000)
- Receivers: FOOD, BEVERAGE (₹1,500 each)

#### Net Cross-Category Impact:

**FOOD Category**:
- Transaction 1: -₹2,000 (gave to BEVERAGE)
- Transaction 2: +₹750 (received from BEVERAGE)
- Transaction 3: +₹1,500 (received from BAKERY)
- **Net**: -₹2,000 + ₹750 + ₹1,500 = **+₹250**

**BEVERAGE Category**:
- Transaction 1: +₹2,000 (received from FOOD)
- Transaction 2: -₹1,500 (gave to FOOD & BAKERY)
- Transaction 3: +₹1,500 (received from BAKERY)
- **Net**: +₹2,000 - ₹1,500 + ₹1,500 = **+₹2,000**

**BAKERY Category**:
- Transaction 1: ₹0 (not involved)
- Transaction 2: +₹750 (received from BEVERAGE)
- Transaction 3: -₹3,000 (gave to FOOD & BEVERAGE)
- **Net**: ₹0 + ₹750 - ₹3,000 = **-₹2,250**

**System Verification**: +₹250 + ₹2,000 + (-₹2,250) = ₹0 ✓

---

## Real-World Application Examples

### Restaurant Chain Scenario

#### Branch Setup:
**Pizza Palace - Main Branch**
- **FOOD Department** → Main Kitchen, Prep Area
- **BEVERAGE Department** → Prep Area, Bar Counter
- **DESSERT Department** → Dessert Station

#### Daily Operations:

**Morning Prep (8:00 AM)**:
- FOOD indents ₹5,000 worth of cheese to Prep Area
- Prep Area is shared between FOOD and BEVERAGE
- **Result**: FOOD (-₹5,000), BEVERAGE (+₹5,000)

**Lunch Rush (12:00 PM)**:
- BEVERAGE indents ₹2,000 worth of ice to Main Kitchen
- Main Kitchen belongs only to FOOD
- **Result**: BEVERAGE (-₹2,000), FOOD (+₹2,000)

**Evening Prep (6:00 PM)**:
- FOOD indents ₹3,000 worth of flour to Dessert Station
- Dessert Station belongs only to DESSERT
- **Result**: FOOD (-₹3,000), DESSERT (+₹3,000)

#### End-of-Day Cross-Category Summary:
- **FOOD**: -₹5,000 + ₹2,000 - ₹3,000 = **-₹6,000**
- **BEVERAGE**: +₹5,000 - ₹2,000 = **+₹3,000**
- **DESSERT**: +₹3,000 = **+₹3,000**
- **Total**: -₹6,000 + ₹3,000 + ₹3,000 = ₹0 ✓

---

## Dashboard Display Examples

### Reconciliation Table View

```
FOOD Category Reconciliation:
├── Vegetables
│   ├── Opening Stock: ₹10,000
│   ├── Purchases: ₹15,000
│   ├── Transfer In/Out: -₹2,000
│   ├── Cross-Category Indents: -₹3,000
│   └── Consumption: ₹18,000
│
├── Dairy Products
│   ├── Opening Stock: ₹8,000
│   ├── Purchases: ₹12,000
│   ├── Transfer In/Out: ₹1,000
│   ├── Cross-Category Indents: +₹1,500
│   └── Consumption: ₹19,500
│
└── Goods from Other Categories' Indents
    ├── Cross-Category Indents: +₹2,000
    └── Consumption: ₹2,000
```

### Transfer Summary Table

```
Department Transfer Analysis:
┌─────────────────┬──────────────┬───────────────┬─────────────────────┐
│ Department      │ Transfer In  │ Transfer Out  │ Cross-Category Net  │
├─────────────────┼──────────────┼───────────────┼─────────────────────┤
│ FOOD            │ ₹25,000      │ ₹18,000       │ -₹3,000            │
│ BEVERAGE        │ ₹12,000      │ ₹8,000        │ +₹2,000            │
│ BAKERY          │ ₹15,000      │ ₹20,000       │ +₹1,000            │
└─────────────────┴──────────────┴───────────────┴─────────────────────┘
```

---

## Troubleshooting Common Scenarios

### Scenario 1: Unbalanced Cross-Category Totals
**Problem**: System shows non-zero total for cross-category indents
**Cause**: Data inconsistency or mapping configuration error
**Solution**: Verify category-workarea mappings and check for duplicate entries

### Scenario 2: Missing Cross-Category Entries
**Problem**: Expected cross-category indent not appearing
**Cause**: Category not mapped to the target workarea
**Solution**: Update category-workarea mapping configuration

### Scenario 3: Incorrect Distribution
**Problem**: Unequal distribution when multiple receivers expected
**Cause**: Mapping configuration shows different workarea ownership
**Solution**: Verify all receiving categories are properly mapped to the workarea

---

## Technical Validation Rules

### Data Quality Checks:
1. **Minimum Threshold**: Only process indents > ₹0.01
2. **Valid Categories**: Source category must exist in mappings
3. **Valid Workareas**: Target workarea must have category mappings
4. **Price Validation**: Unit price (WAC) must be > 0 for value calculation

### System Integrity Checks:
1. **Zero-Sum Validation**: Total cross-category indents must equal zero
2. **Mapping Consistency**: Each workarea must have at least one category mapping
3. **Transaction Completeness**: All indent transactions must have corresponding entries

---

## Conclusion

The cross-category indent system ensures accurate inventory reconciliation by implementing a sophisticated bidirectional tracking mechanism. This system maintains inventory integrity across category boundaries while providing complete transparency and auditability of all cross-category inventory movements.

### Key Benefits Recap:
- ✅ **Accurate Tracking**: Every cross-category movement is precisely recorded
- ✅ **Zero-Sum Integrity**: System-wide balance is always maintained
- ✅ **Fair Distribution**: Multiple receiving categories get equal shares
- ✅ **Complete Transparency**: Full audit trail of all transfers
- ✅ **Scalable Design**: Handles complex multi-category, multi-workarea scenarios
