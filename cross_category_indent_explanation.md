# Cross-Category Indent Calculation - Detailed Explanation

## Overview

Cross-category indents occur when items from one category are transferred (indented) to a workarea that belongs to a different category group. This creates a reconciliation scenario where the source category loses inventory value while the destination category gains it, requiring careful tracking to maintain accurate inventory accounting.

## Your Mapping Configuration

Based on your actual mapping configuration:

### Department Groups
- **LIQUOR Group**: Department 1198
- **FOOD Group**: Departments 1197, 1206, 1205  
- **BEVERAGE Group**: Department 1199

### Category Mappings
- **LIQUOR Group** → LIQUOR category
- **FOOD Group** → BAKERY, FOOD, WOOD FIRE PIZZA categories
- **BEVERAGE Group** → BEVERAGES category

### Workarea Mappings
- **LIQUOR Group** → BAR workarea
- **FOOD Group** → BAKERY, MAIN-KITCHEN workareas
- **BEVERAGE Group** → BAR workarea

## Cross-Category Scenarios

### Scenario 1: FOOD Category Items → BAR Workarea
- **Source**: FOOD category items (owned by FOOD group)
- **Destination**: BAR workarea (owned by LIQUOR & BEVERAGE groups)
- **Impact**: FOOD category loses inventory, LIQUOR & BEVERAGE categories gain inventory
- **Distribution**: Value split equally between LIQUOR and BEVERAGE categories

### Scenario 2: BEVERAGES Category Items → BAKERY/MAIN-KITCHEN Workareas
- **Source**: BEVERAGES category items (owned by BEVERAGE group)
- **Destination**: BAKERY or MAIN-KITCHEN workarea (owned by FOOD group)
- **Impact**: BEVERAGES category loses inventory, FOOD group categories gain inventory
- **Distribution**: Value distributed among BAKERY, FOOD, WOOD FIRE PIZZA categories

### Scenario 3: LIQUOR Category Items → BAKERY/MAIN-KITCHEN Workareas
- **Source**: LIQUOR category items (owned by LIQUOR group)
- **Destination**: BAKERY or MAIN-KITCHEN workarea (owned by FOOD group)
- **Impact**: LIQUOR category loses inventory, FOOD group categories gain inventory
- **Distribution**: Value distributed among BAKERY, FOOD, WOOD FIRE PIZZA categories

## Calculation Logic

### Step 1: Identify Cross-Category Indents
For each inventory consumption record:
1. Extract: Category, Workarea, Indent Quantity, Unit Price
2. Calculate: Indent Value = Quantity × Unit Price
3. Find workarea owner categories from mapping configuration
4. Check if source category ≠ any workarea owner category
5. If different, mark as cross-category indent

### Step 2: Calculate Distribution
For each cross-category indent:
1. **Source Impact**: Source category loses full indent value
2. **Destination Impact**: Value distributed equally among all workarea owner categories (excluding source)
3. **Formula**: Distributed Value = Total Indent Value ÷ Number of Receiving Categories

### Step 3: Reconciliation Impact
For each category involved:
- **Total Given**: Sum of all outbound cross-category indents
- **Total Received**: Sum of all inbound cross-category indents  
- **Net Impact**: Total Received - Total Given

## Zero-Sum System Verification

The cross-category indent system must maintain a zero-sum balance:
- **Total System Impact** = Sum of all category net impacts = 0
- This ensures no inventory value is created or destroyed, only transferred

## Example Calculation

### Sample Transaction
- **Item**: Tomatoes (FOOD category)
- **Indent**: 10 kg to BAR workarea
- **Unit Price**: ₹50/kg
- **Total Value**: ₹500

### Impact Calculation
- **BAR Workarea Owners**: LIQUOR, BEVERAGE categories
- **FOOD Category Impact**: -₹500 (loses inventory)
- **LIQUOR Category Impact**: +₹250 (receives half)
- **BEVERAGE Category Impact**: +₹250 (receives half)
- **Net System Impact**: -500 + 250 + 250 = 0 ✓

## Expected Test Results

When running the test script with your data, expect to see:

### 1. Cross-Category Detection
- Identification of items from one category indented to workareas owned by other categories
- Most common scenarios based on your mapping

### 2. Value Distribution
- Proper splitting of indent values among receiving categories
- Accurate calculation of per-category impacts

### 3. Reconciliation Balance
- Each category's total given vs. total received
- Net impact showing gain/loss for each category
- System-wide zero-sum verification

### 4. Transaction Details
- Source category, destination workarea, and receiving categories
- Item details and value calculations
- Distribution logic applied

## Business Impact

### Inventory Accuracy
- Ensures accurate tracking of inventory movements across categories
- Maintains proper cost allocation for each category

### Financial Reconciliation
- Provides clear audit trail for cross-category transfers
- Enables accurate P&L calculation per category

### Operational Insights
- Identifies patterns of cross-category usage
- Helps optimize inventory allocation and purchasing decisions

## Validation Points

The test script validates:
1. **Mapping Accuracy**: Correct category-workarea relationships
2. **Detection Logic**: Proper identification of cross-category indents
3. **Distribution Math**: Accurate value splitting calculations
4. **Zero-Sum Balance**: System maintains inventory value conservation
5. **Data Integrity**: Handles edge cases and invalid data gracefully

## Running the Test

```bash
# Ensure your CSV file is named 'inventory_consumption.csv'
python cross_category_indent_test_script.py
```

The script will output detailed results showing:
- Number of cross-category transactions found
- Sample transaction details
- Category-wise reconciliation impact
- Zero-sum system verification
- Summary statistics

This comprehensive testing approach ensures the cross-category indent logic works correctly with your actual data and mapping configuration.
