# Manual Cross-Category Indent Validation Guide

## Overview
This guide helps you manually validate cross-category indent calculations using CSV analysis tools like Excel, Google Sheets, or Python pandas.

## Step-by-Step Manual Validation Process

### Step 1: Understand Your Mapping Configuration

**Your Current Mapping:**
- **LIQUOR** → BAR workarea
- **BEVERAGES** → BAR workarea  
- **FOOD** → BAKERY, MAIN-KITCHEN workareas
- **BAKERY** → BAKERY, MAIN-KITCHEN workareas
- **WOOD FIRE PIZZA** → BAKERY, MAIN-KITCHEN workareas

**Key Point:** BAR workarea is shared between LIQUOR and BEVERAGES categories.

### Step 2: Filter the CSV Data

Open your `inventory_consumption.csv` file and apply these filters:

#### Filter 1: Find All Cross-Category Indents Involving BEVERAGES

**Criteria:**
1. `Category` = "BEVERAGES" AND `WorkArea Indent` > 0 (BEVERAGES giving to others)
2. `WorkArea` = "BAR" AND `Category` ≠ "BEVERAGES" AND `Category` ≠ "LIQUOR" AND `WorkArea Indent` > 0 (Others giving to BAR, which BEVERAGES shares)
3. `WorkArea` = "BAR" AND `Category` = "LIQUOR" AND `WorkArea Indent` > 0 (LIQUOR giving to BAR, which BEVERAGES receives)

### Step 3: Manual Calculation Steps

#### A. BEVERAGES Giving Transactions (Negative Impact)

**Filter:** `Category` = "BEVERAGES" AND `WorkArea Indent` > 0

**Calculation for each row:**
```
Indent Value = WorkArea Indent × WAC(incl.tax,etc)
```

**Expected Results:**
- BEVERAGES → BAR: Items going to LIQUOR (since BAR is shared)
- BEVERAGES → MAIN-KITCHEN: Items going to FOOD group categories

#### B. BEVERAGES Receiving Transactions (Positive Impact)

**Case 1: From FOOD items to BAR**
- **Filter:** `Category` = "FOOD" AND `WorkArea` = "BAR" AND `WorkArea Indent` > 0
- **Calculation:** `Indent Value ÷ 2` (split between LIQUOR and BEVERAGES)

**Case 2: From LIQUOR items to BAR**  
- **Filter:** `Category` = "LIQUOR" AND `WorkArea` = "BAR" AND `WorkArea Indent` > 0
- **Calculation:** `Full Indent Value` (BEVERAGES gets full amount since LIQUOR can't receive from itself)

### Step 4: Excel/Google Sheets Formulas

#### Create Helper Columns:

**Column A: Indent Value**
```excel
=IF(AND(NOT(ISBLANK(WorkAreaIndent)), NOT(ISBLANK(WAC))), WorkAreaIndent * WAC, 0)
```

**Column B: BEVERAGES Giving**
```excel
=IF(AND(Category="BEVERAGES", IndentValue>0), -IndentValue, 0)
```

**Column C: BEVERAGES Receiving from FOOD**
```excel
=IF(AND(Category="FOOD", WorkArea="BAR", IndentValue>0), IndentValue/2, 0)
```

**Column D: BEVERAGES Receiving from LIQUOR**
```excel
=IF(AND(Category="LIQUOR", WorkArea="BAR", IndentValue>0), IndentValue, 0)
```

**Column E: BEVERAGES Net Impact**
```excel
=B + C + D
```

#### Final Calculation:
```excel
=SUM(E:E)
```

### Step 5: Expected Manual Results

Based on the validator output, you should find:

#### BEVERAGES Giving:
- **JUICES to BAR**: -₹11,219
- **DRINKS to BAR**: -₹1,00,620  
- **SYRUP to BAR**: -₹25,516
- **JUICES to MAIN-KITCHEN**: -₹816
- **Total Given**: -₹1,38,171

#### BEVERAGES Receiving:
- **From FOOD to BAR** (split with LIQUOR): +₹35,287
- **From LIQUOR to BAR** (full amount): +₹3,81,736
- **Total Received**: +₹4,17,023

#### Net Impact:
```
₹4,17,023 - ₹1,38,171 = ₹2,78,852 ≈ ₹2,78,853
```

### Step 6: Verification Checklist

✅ **Data Quality Checks:**
- [ ] All `WorkArea Indent` values are numeric
- [ ] All `WAC(incl.tax,etc)` values are numeric  
- [ ] No missing Category or WorkArea values
- [ ] Indent values > ₹0.01 threshold

✅ **Logic Checks:**
- [ ] BEVERAGES items to BAR go to LIQUOR (negative for BEVERAGES)
- [ ] LIQUOR items to BAR go to BEVERAGES (positive for BEVERAGES)
- [ ] FOOD items to BAR split between LIQUOR and BEVERAGES
- [ ] Cross-category flows maintain zero-sum balance

✅ **Calculation Checks:**
- [ ] Indent Value = Quantity × Unit Price
- [ ] Shared workarea values split equally among owners
- [ ] Same-category indents excluded from cross-category calculations

### Step 7: Common Issues and Solutions

**Issue 1: Values don't match exactly**
- **Cause:** Rounding differences in Excel vs. system
- **Solution:** Use more decimal places in calculations

**Issue 2: Missing transactions**
- **Cause:** Data filtering too restrictive
- **Solution:** Check for variations in category names (spaces, case)

**Issue 3: Wrong distribution**
- **Cause:** Incorrect workarea ownership understanding
- **Solution:** Verify which categories map to which workareas

### Step 8: Python Pandas Alternative

If you prefer Python, use the helper script `manual_validation_helper.py` to:
- Load and filter the CSV data
- Apply the exact same logic as manual calculation
- Generate detailed transaction reports
- Cross-verify with dashboard results

This manual process will give you complete confidence in the cross-category indent calculations!
