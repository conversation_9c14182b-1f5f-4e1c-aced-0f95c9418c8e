#!/usr/bin/env python3
"""
Cross-Category Indent Calculation Test Script
============================================

This script validates the cross-category indent calculations based on your actual
inventory consumption data and mapping configuration.

Based on your mapping configuration:
- LIQUOR Group: Department 1198 → LIQUOR category → BAR workarea
- FOOD Group: Departments 1197,1206,1205 → BAKERY,FOOD,WOOD FIRE PIZZA categories → BAKERY,MAIN-KITCHEN workareas  
- BEVERAGE Group: Department 1199 → BEVERAGES category → BAR workarea

Cross-category scenarios:
1. FOOD category items indented to BAR workarea (owned by LIQUOR & BEVERAGE groups)
2. BEVERAGES category items indented to BAKERY/MAIN-KITCHEN workareas (owned by FOOD group)
3. LIQUOR category items indented to BAKERY/MAIN-KITCHEN workareas (owned by FOOD group)
"""

import pandas as pd
import json
from typing import Dict, List, Tuple

# Your actual mapping configuration
MAPPING_CONFIG = {
    "departmentGroups": [
        {
            "id": "805e20c5-4a17-4e50-ad45-a6ca6cd2dbcf",
            "name": "LIQUOR",
            "departmentIds": ["1198"],
            "isActive": True
        },
        {
            "id": "ad35382c-de39-4067-a21d-c23c4414a3f5",
            "name": "FOOD",
            "departmentIds": ["1197", "1206", "1205"],
            "isActive": True
        },
        {
            "id": "404ca2c3-9b05-4676-940f-104db1a399c3",
            "name": "BEVERAGE",
            "departmentIds": ["1199"],
            "isActive": True
        },
        {
            "id": "others-group-id",
            "name": "OTHERS",
            "departmentIds": [],
            "isActive": True
        }
    ],
    "departmentGroupCategoryMappings": [
        {
            "groupId": "805e20c5-4a17-4e50-ad45-a6ca6cd2dbcf",
            "groupName": "LIQUOR",
            "categories": ["LIQUOR"]
        },
        {
            "groupId": "ad35382c-de39-4067-a21d-c23c4414a3f5",
            "groupName": "FOOD",
            "categories": ["BAKERY", "FOOD", "WOOD FIRE PIZZA"]
        },
        {
            "groupId": "404ca2c3-9b05-4676-940f-104db1a399c3",
            "groupName": "BEVERAGE",
            "categories": ["BEVERAGES"]
        },
        {
            "groupId": "others-group-id",
            "groupName": "OTHERS",
            "categories": ["NON FOOD", "ASSETS", "OTHERS"]
        }
    ],
    "departmentGroupWorkareaMappings": [
        {
            "groupId": "805e20c5-4a17-4e50-ad45-a6ca6cd2dbcf",
            "groupName": "LIQUOR",
            "workAreas": ["BAR"]
        },
        {
            "groupId": "ad35382c-de39-4067-a21d-c23c4414a3f5",
            "groupName": "FOOD",
            "workAreas": ["BAKERY", "MAIN-KITCHEN"]
        },
        {
            "groupId": "404ca2c3-9b05-4676-940f-104db1a399c3",
            "groupName": "BEVERAGE",
            "workAreas": ["BAR"]
        },
        {
            "groupId": "others-group-id",
            "groupName": "OTHERS",
            "workAreas": ["BAKERY", "MAIN-KITCHEN", "BAR"]
        }
    ]
}

def build_category_workarea_mappings(config: Dict) -> List[Dict]:
    """Build category-workarea mappings from the configuration."""
    mappings = []
    
    # Create mapping from group to categories and workareas
    group_categories = {m['groupId']: m['categories'] for m in config['departmentGroupCategoryMappings']}
    group_workareas = {m['groupId']: m['workAreas'] for m in config['departmentGroupWorkareaMappings']}
    
    # For each group, map all its categories to all its workareas
    for group in config['departmentGroups']:
        group_id = group['id']
        categories = group_categories.get(group_id, [])
        workareas = group_workareas.get(group_id, [])
        
        for category in categories:
            mappings.append({
                'categoryName': category,
                'workAreas': workareas
            })
    
    return mappings

def detect_cross_category_indents(df: pd.DataFrame, category_workarea_mappings: List[Dict]) -> List[Dict]:
    """
    Detect cross-category indent transactions from the consumption data.
    
    Returns list of cross-category indent transactions with details.
    """
    # Build category to workareas mapping
    category_to_workareas = {}
    for mapping in category_workarea_mappings:
        category_name = mapping['categoryName']
        work_areas = mapping['workAreas']
        category_to_workareas[category_name] = work_areas
    
    cross_category_indents = []
    
    # Process each row in the consumption data
    for _, row in df.iterrows():
        category = str(row.get('Category', '')).strip()
        subcategory = str(row.get('Sub Category', '')).strip()
        workarea = str(row.get('WorkArea', '')).strip()
        item_name = str(row.get('Item Name', '')).strip()
        item_code = str(row.get('Item Code', '')).strip()
        
        # Get indent quantity and unit price
        try:
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_value = indent_qty * unit_price
        except (ValueError, TypeError):
            continue
            
        # Skip if no indent or invalid data
        if abs(indent_value) <= 0.01 or not category or not workarea:
            continue
            
        # Find workarea owner categories
        workarea_owner_categories = []
        for cat_name, work_areas in category_to_workareas.items():
            if workarea in work_areas:
                workarea_owner_categories.append(cat_name)
        
        # Check if this is cross-category
        cross_category_owners = [owner for owner in workarea_owner_categories if owner != category]
        
        if cross_category_owners:
            # This is a cross-category indent
            distributed_value = indent_value / len(cross_category_owners)
            
            cross_category_indents.append({
                'source_category': category,
                'source_subcategory': subcategory,
                'item_name': item_name,
                'item_code': item_code,
                'workarea': workarea,
                'indent_qty': indent_qty,
                'unit_price': unit_price,
                'total_indent_value': indent_value,
                'workarea_owners': workarea_owner_categories,
                'receiving_categories': cross_category_owners,
                'distributed_value_per_receiver': distributed_value,
                'transaction_type': 'Cross-Category Indent'
            })
    
    return cross_category_indents

def calculate_cross_category_reconciliation(cross_category_indents: List[Dict]) -> Dict:
    """
    Calculate the reconciliation impact of cross-category indents.
    
    Returns dictionary with category-wise impact.
    """
    reconciliation_impact = {}
    
    for indent in cross_category_indents:
        source_category = indent['source_category']
        receiving_categories = indent['receiving_categories']
        total_value = indent['total_indent_value']
        distributed_value = indent['distributed_value_per_receiver']
        
        # Initialize categories if not exists
        if source_category not in reconciliation_impact:
            reconciliation_impact[source_category] = {
                'total_given': 0,
                'total_received': 0,
                'net_impact': 0,
                'transactions': []
            }
        
        for receiving_category in receiving_categories:
            if receiving_category not in reconciliation_impact:
                reconciliation_impact[receiving_category] = {
                    'total_given': 0,
                    'total_received': 0,
                    'net_impact': 0,
                    'transactions': []
                }
        
        # Record the giving transaction
        reconciliation_impact[source_category]['total_given'] += total_value
        reconciliation_impact[source_category]['transactions'].append({
            'type': 'outbound',
            'amount': -total_value,
            'counterpart_categories': receiving_categories,
            'item': indent['item_name'],
            'workarea': indent['workarea']
        })
        
        # Record the receiving transactions
        for receiving_category in receiving_categories:
            reconciliation_impact[receiving_category]['total_received'] += distributed_value
            reconciliation_impact[receiving_category]['transactions'].append({
                'type': 'inbound',
                'amount': distributed_value,
                'counterpart_category': source_category,
                'item': indent['item_name'],
                'workarea': indent['workarea']
            })
    
    # Calculate net impact for each category
    for category, data in reconciliation_impact.items():
        data['net_impact'] = data['total_received'] - data['total_given']
    
    return reconciliation_impact

def run_cross_category_test(csv_file_path: str):
    """
    Main test function to validate cross-category indent calculations.
    """
    print("=" * 80)
    print("CROSS-CATEGORY INDENT CALCULATION TEST")
    print("=" * 80)
    
    # Load the consumption data
    print(f"\n1. Loading consumption data from: {csv_file_path}")
    try:
        df = pd.read_csv(csv_file_path)
        print(f"   ✓ Loaded {len(df)} records")
    except Exception as e:
        print(f"   ✗ Error loading data: {e}")
        return
    
    # Build category-workarea mappings
    print("\n2. Building category-workarea mappings from configuration...")
    category_workarea_mappings = build_category_workarea_mappings(MAPPING_CONFIG)
    
    print("   Category-Workarea Mappings:")
    for mapping in category_workarea_mappings:
        print(f"   - {mapping['categoryName']} → {mapping['workAreas']}")
    
    # Detect cross-category indents
    print("\n3. Detecting cross-category indent transactions...")
    cross_category_indents = detect_cross_category_indents(df, category_workarea_mappings)
    print(f"   ✓ Found {len(cross_category_indents)} cross-category indent transactions")
    
    if not cross_category_indents:
        print("   ℹ No cross-category indents found in the data")
        return
    
    # Display sample cross-category indents
    print("\n4. Sample Cross-Category Indent Transactions:")
    print("-" * 80)
    for i, indent in enumerate(cross_category_indents[:5]):  # Show first 5
        print(f"\nTransaction {i+1}:")
        print(f"   Item: {indent['item_name']} ({indent['item_code']})")
        print(f"   Source: {indent['source_category']} → {indent['workarea']} workarea")
        print(f"   Indent: {indent['indent_qty']} units × ₹{indent['unit_price']:.2f} = ₹{indent['total_indent_value']:.2f}")
        print(f"   Workarea Owners: {indent['workarea_owners']}")
        print(f"   Receiving Categories: {indent['receiving_categories']}")
        print(f"   Distributed Value: ₹{indent['distributed_value_per_receiver']:.2f} per receiver")
    
    if len(cross_category_indents) > 5:
        print(f"\n   ... and {len(cross_category_indents) - 5} more transactions")
    
    # Calculate reconciliation impact
    print("\n5. Calculating Cross-Category Reconciliation Impact:")
    print("-" * 80)
    reconciliation_impact = calculate_cross_category_reconciliation(cross_category_indents)
    
    total_system_impact = 0
    for category, impact in reconciliation_impact.items():
        print(f"\n{category} Category:")
        print(f"   Total Given:    ₹{impact['total_given']:,.2f}")
        print(f"   Total Received: ₹{impact['total_received']:,.2f}")
        print(f"   Net Impact:     ₹{impact['net_impact']:,.2f}")
        print(f"   Transactions:   {len(impact['transactions'])}")
        total_system_impact += impact['net_impact']
    
    # Verify zero-sum system
    print(f"\n6. Zero-Sum System Verification:")
    print("-" * 80)
    print(f"Total System Impact: ₹{total_system_impact:.2f}")
    if abs(total_system_impact) < 0.01:
        print("✓ PASS: System maintains zero-sum balance")
    else:
        print("✗ FAIL: System is not balanced!")
    
    # Summary statistics
    print(f"\n7. Summary Statistics:")
    print("-" * 80)
    total_cross_category_value = sum(indent['total_indent_value'] for indent in cross_category_indents)
    print(f"Total Cross-Category Indent Value: ₹{total_cross_category_value:,.2f}")
    print(f"Number of Cross-Category Transactions: {len(cross_category_indents)}")
    print(f"Categories Involved: {len(reconciliation_impact)}")

    # Category breakdown
    category_counts = {}
    for indent in cross_category_indents:
        source = indent['source_category']
        category_counts[source] = category_counts.get(source, 0) + 1

    print(f"\nTransactions by Source Category:")
    for category, count in sorted(category_counts.items()):
        print(f"   {category}: {count} transactions")

    # Dashboard Format Comparison
    print(f"\n8. Dashboard Format Comparison:")
    print("-" * 80)
    print("Cross-Category Indent Values (matching your dashboard format):")
    print()

    # Format values in Indian numbering system like your dashboard
    def format_indian_currency(value):
        """Format currency in Indian numbering system"""
        if abs(value) < 0.01:
            return "-"

        # Convert to string and handle negative
        is_negative = value < 0
        abs_value = abs(value)

        # Format with Indian comma separation
        if abs_value >= 10000000:  # 1 crore
            formatted = f"{abs_value/10000000:.2f} Cr"
        elif abs_value >= 100000:  # 1 lakh
            formatted = f"{abs_value/100000:.2f} L"
        else:
            # Standard formatting for smaller amounts
            formatted = f"{abs_value:,.0f}"
            # Convert to Indian format (lakhs, thousands)
            if ',' in formatted:
                parts = formatted.split(',')
                if len(parts) == 2:  # thousands
                    formatted = f"{parts[0]},{parts[1]}"
                elif len(parts) == 3:  # lakhs
                    formatted = f"{parts[0]},{parts[1]},{parts[2]}"
                elif len(parts) == 4:  # crores
                    formatted = f"{parts[0]},{parts[1]},{parts[2]},{parts[3]}"

        return f"-{formatted}" if is_negative else formatted

    # Sort categories to match dashboard order
    dashboard_categories = [
        'LIQUOR', 'BEVERAGES', 'FOOD', 'BAKERY', 'WOOD FIRE PIZZA',
        'NON FOOD', 'ASSETS', 'OTHERS'
    ]

    for category in dashboard_categories:
        if category in reconciliation_impact:
            net_impact = reconciliation_impact[category]['net_impact']
            formatted_value = format_indian_currency(net_impact)
            print(f"{category:<20} {formatted_value:>15}")

    print()
    print("Note: Values may differ slightly from dashboard due to:")
    print("- Different rounding methods")
    print("- Additional business logic in dashboard")
    print("- Data filtering or processing differences")
    print("- Special handling of certain categories or items")

if __name__ == "__main__":
    # Run the test with your actual data file
    csv_file_path = "/home/<USER>/other/digii/digitoryjobsv4/inventory_consumption.csv"
    run_cross_category_test(csv_file_path)
